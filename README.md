# Hexagonal Grid POI Visualization

This project creates hexagonal grid visualizations for Point of Interest (POI) data from CSV files.

## Features

- **Hexagonal Grid Rendering**: Each data point is rendered as a proper hexagon with 150m diagonal length
- **Dual Visualizations**: 
  - Count data with white to #E07F86 color mapping
  - Distance data with #AC4828 to white color mapping
- **Multi-POI Support**: Handles 14 different POI types in Chinese
- **Tessellation**: Hexagons tile seamlessly without gaps
- **Performance Optimized**: Configurable sampling for large datasets

## Data Requirements

The script reads CSV files with the following columns:
- `grid_lat`: Latitude of hexagon center
- `grid_lon`: Longitude of hexagon center  
- `poi_type`: Type of POI (Chinese text)
- `count`: Number of POIs in the hexagon
- `min_distance`: Minimum distance to nearest POI

## POI Types Supported

The script filters for these 14 POI types:
- 文化教育 (Culture & Education)
- 亲子 (Parent-Child)
- 美食 (Food)
- 休闲娱乐 (Leisure & Entertainment)
- 医疗 (Medical)
- 购物 (Shopping)
- 运动健身 (Sports & Fitness)
- 旅游景点 (Tourist Attractions)
- 金融 (Finance)
- 丽人 (Beauty)
- 交通设施 (Transportation)
- 酒店 (Hotels)
- 生活服务 (Life Services)
- 办公住宅 (Office & Residential)

## Usage

### Quick Start (Sample Data)
```python
# Current settings in hexagon_visualization.py
USE_SAMPLE = True
SAMPLE_FRACTION = 0.02  # 2% of data
```

Run the script:
```bash
python hexagon_visualization.py
```

### Full Dataset
To process the complete dataset:
1. Edit `hexagon_visualization.py`
2. Set `USE_SAMPLE = False`
3. Run the script (may take significant time)

## Output Files

The script generates two PNG files:
- `hexagon_count_visualization_sample_X.Xpct.png`: Count data visualization
- `hexagon_distance_visualization_sample_X.Xpct.png`: Distance data visualization

For full dataset, files are named without the sample suffix.

## Technical Details

### Hexagon Calculations
- Diagonal length: 150 meters
- Radius: 75 meters
- Coordinate conversion based on latitude ~39.9° (Beijing area)
- Proper tessellation using 6-vertex polygons

### Color Mappings
1. **Count Data**: Linear interpolation from white (0) to #E07F86 (max)
2. **Distance Data**: Transformed using `max(0, min_distance - 150)`, linear interpolation from #AC4828 (0) to white (max)

### Performance Features
- Non-interactive matplotlib backend for faster rendering
- Memory cleanup between figures
- Progress indicators for long-running processes
- Configurable sampling rates

## Requirements

```python
pandas
matplotlib
numpy
```

## Data Statistics

From the current dataset:
- Total filtered data points: 5,200,148
- 15 unique POI types (14 target + 1 additional)
- Geographic coverage: Beijing area (~39.9° latitude)

## Memory Considerations

- 2% sample: ~104,000 data points (recommended for testing)
- Full dataset: 5.2M+ data points (requires significant RAM)
- Each hexagon is rendered as a 6-vertex polygon with borders
