# -*- coding: utf-8 -*-
"""
Hexagonal Grid Visualization for POI Data
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.collections as mcollections
import glob
import os
import numpy as np
import gc

# 设置matplotlib后端为非交互式
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置文件路径 - 使用当前目录
path = '.'
csv_files = glob.glob(os.path.join(path, "*.csv"))

# 指定的POI类型
poi_types = ['文化教育', '亲子', '美食', '休闲娱乐', '医疗', '购物', '运动健身', 
             '旅游景点', '金融', '丽人', '交通设施', '酒店', '生活服务', '办公住宅']

# 读取所有CSV文件
dfs = []
for file in csv_files:
    try:
        df0 = pd.read_csv(file)
        dfs.append(df0)
    except Exception as e:
        print(f"Error reading {file}: {e}")

if not dfs:
    print("No valid CSV files were read.")
    exit()

# 合并所有数据
df = pd.concat(dfs, ignore_index=True)

# 检查实际的POI类型
print("Actual POI types in data:")
print(df['poi_type'].unique())
print(f"Total unique POI types: {len(df['poi_type'].unique())}")

# 过滤POI类型
df = df[df['poi_type'].isin(poi_types)]
print(f"Data points after filtering: {len(df)}")

# 数据采样设置
USE_SAMPLE = False  # 设置为False以使用完整数据集
SAMPLE_FRACTION = 0.02  # 采样比例 (0.02 = 2%)

if USE_SAMPLE:
    df = df.sample(frac=SAMPLE_FRACTION, random_state=42)
    print(f"Using sample data: {len(df)} data points ({SAMPLE_FRACTION*100}% of filtered data)")
else:
    print(f"Using full dataset: {len(df)} data points")

# 计算六边形参数
diagonal_meters = 150
r_meters = diagonal_meters / 2
lat = 39.9
meters_per_degree_lat = 111000
meters_per_degree_lon = 111000 * np.cos(np.radians(lat))
r_degrees_lat = r_meters / meters_per_degree_lat
r_degrees_lon = r_meters / meters_per_degree_lon

def create_hexagon_vertices(center_lon, center_lat, r_lon, r_lat):
    """创建六边形顶点"""
    angles = np.linspace(0, 2 * np.pi, 7)
    x = center_lon + r_lon * np.cos(angles)
    y = center_lat + r_lat * np.sin(angles)
    return np.column_stack((x, y))

# 创建颜色映射
cmap_count = mcolors.LinearSegmentedColormap.from_list("", ["white", "#E07F86"])
cmap_distance = mcolors.LinearSegmentedColormap.from_list("", ["#AC4828", "white"])

# 第一个图：Count数据
print("Creating count visualization...")
fig_count, axes_count = plt.subplots(3, 5, figsize=(100,60))
axes_count = axes_count.ravel()

for i, poi in enumerate(poi_types):
    print(f"Processing count data for {poi} ({i+1}/{len(poi_types)})")
    poi_data = df[df['poi_type'] == poi]
    
    if poi_data.empty:
        axes_count[i].set_title(f'{poi} (Count) - No Data')
        continue
    
    # 准备数据
    centers = poi_data[['grid_lon', 'grid_lat']].values
    values = poi_data['count'].values
    
    # 创建六边形
    verts = []
    for center in centers:
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        verts.append(hex_verts)
    
    # 归一化
    max_count = np.max(values) if len(values) > 0 else 1
    norm = mcolors.Normalize(vmin=0, vmax=max_count)
    
    # 创建多边形集合
    pc = mcollections.PolyCollection(
        verts, array=values, cmap=cmap_count, norm=norm,
        edgecolors='black', linewidths=0.1
    )
    
    axes_count[i].add_collection(pc)
    axes_count[i].set_title(f'{poi} (Count)')
    axes_count[i].set_xlabel('Longitude')
    axes_count[i].set_ylabel('Latitude')
    axes_count[i].set_aspect('equal')
    
    # 设置坐标轴范围
    if len(centers) > 0:
        axes_count[i].set_xlim(centers[:, 0].min() - r_degrees_lon, 
                              centers[:, 0].max() + r_degrees_lon)
        axes_count[i].set_ylim(centers[:, 1].min() - r_degrees_lat, 
                              centers[:, 1].max() + r_degrees_lat)
    
    # 添加颜色条
    fig_count.colorbar(pc, ax=axes_count[i], label='Count')

# 删除多余子图
for i in range(len(poi_types), len(axes_count)):
    fig_count.delaxes(axes_count[i])

fig_count.tight_layout()
gc.collect()  # 清理内存

# 第二个图：Distance数据
print("Creating distance visualization...")
fig_distance, axes_distance = plt.subplots(3, 5, figsize=(100,60))
axes_distance = axes_distance.ravel()

for i, poi in enumerate(poi_types):
    print(f"Processing distance data for {poi} ({i+1}/{len(poi_types)})")
    poi_data = df[df['poi_type'] == poi].copy()
    
    if poi_data.empty:
        axes_distance[i].set_title(f'{poi} (Distance) - No Data')
        continue
    
    # 转换距离数据
    poi_data['adjusted_distance'] = poi_data['min_distance'].apply(lambda x: max(0, x - 150))
    
    # 准备数据
    centers = poi_data[['grid_lon', 'grid_lat']].values
    values = poi_data['adjusted_distance'].values
    
    # 创建六边形
    verts = []
    for center in centers:
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        verts.append(hex_verts)
    
    # 归一化
    max_distance = np.max(values) if len(values) > 0 else 1
    norm = mcolors.Normalize(vmin=0, vmax=max_distance)
    
    # 创建多边形集合
    pc = mcollections.PolyCollection(
        verts, array=values, cmap=cmap_distance, norm=norm,
        edgecolors='black', linewidths=0.1
    )
    
    axes_distance[i].add_collection(pc)
    axes_distance[i].set_title(f'{poi} (Distance)')
    axes_distance[i].set_xlabel('Longitude')
    axes_distance[i].set_ylabel('Latitude')
    axes_distance[i].set_aspect('equal')
    
    # 设置坐标轴范围
    if len(centers) > 0:
        axes_distance[i].set_xlim(centers[:, 0].min() - r_degrees_lon, 
                                 centers[:, 0].max() + r_degrees_lon)
        axes_distance[i].set_ylim(centers[:, 1].min() - r_degrees_lat, 
                                 centers[:, 1].max() + r_degrees_lat)
    
    # 添加颜色条
    fig_distance.colorbar(pc, ax=axes_distance[i], label='Adjusted Distance (m)')

# 删除多余子图
for i in range(len(poi_types), len(axes_distance)):
    fig_distance.delaxes(axes_distance[i])

fig_distance.tight_layout()

# 保存图像
print("Saving figures...")
sample_suffix = f"_sample_{SAMPLE_FRACTION*100:.1f}pct" if USE_SAMPLE else "_full"
count_filename = f'hexagon_count_visualization{sample_suffix}.png'
distance_filename = f'hexagon_distance_visualization{sample_suffix}.png'

fig_count.savefig(count_filename, dpi=900, bbox_inches='tight')
fig_distance.savefig(distance_filename, dpi=900, bbox_inches='tight')
print(f"Figures saved as '{count_filename}' and '{distance_filename}'")

plt.close(fig_count)
plt.close(fig_distance)

print(f"Processed {len(df)} data points")
if USE_SAMPLE:
    print(f"Visualization complete with {SAMPLE_FRACTION*100}% sample.")
    print("To use the full dataset, set USE_SAMPLE = False in the script.")
    print("Warning: Full dataset processing may take significant time and memory.")
else:
    print("Visualization complete with full dataset.")